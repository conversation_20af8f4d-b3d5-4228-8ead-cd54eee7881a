/* HERO SECTION MODERNE - DESIGN PREMIUM */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;
  padding: 120px 0 80px;

  // Arrière-plan animé moderne
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.3;
      animation: float 8s ease-in-out infinite;

      &.orb-1 {
        width: 300px;
        height: 300px;
        background: linear-gradient(45deg, #6366f1, #8b5cf6);
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg, #06b6d4, #3b82f6);
        top: 60%;
        right: 20%;
        animation-delay: 3s;
      }

      &.orb-3 {
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg, #ec4899, #f59e0b);
        bottom: 20%;
        left: 30%;
        animation-delay: 6s;
      }
    }

    .floating-elements {
      position: absolute;
      width: 100%;
      height: 100%;

      .floating-element {
        position: absolute;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        animation: floatSlow 12s ease-in-out infinite;

        &.element-1 {
          top: 15%;
          left: 15%;
          animation-delay: 0s;
        }

        &.element-2 {
          top: 25%;
          right: 25%;
          animation-delay: 3s;
        }

        &.element-3 {
          bottom: 35%;
          left: 25%;
          animation-delay: 6s;
        }

        &.element-4 {
          bottom: 15%;
          right: 15%;
          animation-delay: 9s;
        }
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .hero-content {
    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 50px;
      backdrop-filter: blur(20px);
      font-size: 14px;
      font-weight: 600;
      color: white;
      margin-bottom: 32px;
      position: relative;
      overflow: hidden;
      transition: all 0.4s ease;

      .badge-icon {
        color: #fbbf24;
        animation: pulse 2s ease-in-out infinite;
      }

      .badge-glow {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        animation: shimmer 3s ease-in-out infinite;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
      }
    }

    .hero-title {
      font-size: clamp(2.5rem, 6vw, 4.5rem);
      font-weight: 900;
      line-height: 1.1;
      margin-bottom: 24px;
      letter-spacing: -0.02em;

      .title-line {
        display: block;
        color: white;
      }

      .title-highlight {
        display: block;
        background: linear-gradient(
          135deg,
          #6366f1 0%,
          #8b5cf6 50%,
          #ec4899 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 100%;
          height: 4px;
          background: linear-gradient(135deg, #6366f1, #8b5cf6);
          border-radius: 2px;
          opacity: 0.6;
        }
      }
    }

    .hero-subtitle {
      font-size: 1.25rem;
      line-height: 1.7;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 48px;
      max-width: 600px;
      font-weight: 400;
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      margin-bottom: 64px;

      @media (max-width: 640px) {
        flex-direction: column;
        gap: 16px;
      }

      .btn-primary,
      .btn-secondary {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 18px 32px;
        border-radius: 16px;
        font-weight: 600;
        font-size: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-width: 200px;
        justify-content: center;

        .btn-icon {
          transition: transform 0.3s ease;
        }

        .btn-ripple {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: width 0.6s, height 0.6s;
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);

          .btn-icon {
            transform: translateX(4px);
          }

          .btn-ripple {
            width: 300px;
            height: 300px;
          }
        }

        &:active {
          transform: translateY(-1px);
        }
      }

      .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4);

        &:hover {
          box-shadow: 0 12px 40px rgba(99, 102, 241, 0.6);
        }
      }

      .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(20px);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }
    .hero-stats {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
        margin-bottom: 32px;

        @media (max-width: 640px) {
          grid-template-columns: 1fr;
          gap: 24px;
        }

        .stat-item {
          text-align: center;
          position: relative;

          .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: white;
            line-height: 1;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
            margin-bottom: 12px;
          }

          .stat-progress {
            width: 100%;
            height: 3px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;

            .progress-bar {
              height: 100%;
              background: linear-gradient(90deg, #6366f1, #8b5cf6);
              border-radius: 2px;
              width: 0;
              animation: progressFill 2s ease-out forwards;
              animation-delay: calc(var(--index, 0) * 0.3s + 1s);
            }
          }
        }
      }

      .trust-badges {
        display: flex;
        align-items: center;
        gap: 24px;
        padding-top: 32px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: 16px;
        }

        .trust-text {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
        }

        .badges {
          display: flex;
          gap: 16px;

          .badge-item {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  // Visuel moderne avec animations 3D
  .hero-visual {
    position: relative;

    .visual-container {
      position: relative;
      perspective: 1000px;

      .dashboard-main {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        transform: rotateY(-5deg) rotateX(5deg);
        transition: transform 0.6s ease;
        animation: dashboardFloat 6s ease-in-out infinite;

        &:hover {
          transform: rotateY(-2deg) rotateX(2deg) scale(1.02);
        }

        .dashboard-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px 24px;
          background: linear-gradient(135deg, #f8fafc, #e2e8f0);
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);

          .header-dots {
            display: flex;
            gap: 8px;

            .dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;

              &.red {
                background: #ef4444;
              }
              &.yellow {
                background: #f59e0b;
              }
              &.green {
                background: #10b981;
              }
            }
          }

          .header-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }
        }

        .dashboard-content {
          padding: 24px;

          .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 24px;

            .card {
              background: white;
              border-radius: 12px;
              padding: 16px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              text-align: center;
              animation: cardPulse 3s ease-in-out infinite;

              &.card-1 {
                animation-delay: 0s;
              }
              &.card-2 {
                animation-delay: 1s;
              }
              &.card-3 {
                animation-delay: 2s;
              }

              .card-icon {
                font-size: 24px;
                margin-bottom: 8px;
              }

              .card-value {
                font-size: 20px;
                font-weight: 700;
                color: #1f2937;
                margin-bottom: 4px;
              }

              .card-label {
                font-size: 12px;
                color: #6b7280;
                font-weight: 500;
              }
            }
          }

          .dashboard-chart {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            .chart-bars {
              display: flex;
              align-items: end;
              gap: 8px;
              height: 60px;

              .bar {
                flex: 1;
                background: linear-gradient(to top, #6366f1, #8b5cf6);
                border-radius: 4px 4px 0 0;
                animation: barGrow 2s ease-out forwards;
                transform-origin: bottom;
                transform: scaleY(0);

                &:nth-child(1) {
                  animation-delay: 0.2s;
                }
                &:nth-child(2) {
                  animation-delay: 0.4s;
                }
                &:nth-child(3) {
                  animation-delay: 0.6s;
                }
                &:nth-child(4) {
                  animation-delay: 0.8s;
                }
                &:nth-child(5) {
                  animation-delay: 1s;
                }
              }
            }
          }
        }
      }

      .floating-cards {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;

        .floating-card {
          position: absolute;
          background: white;
          border-radius: 12px;
          padding: 12px 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          animation: floatCard 4s ease-in-out infinite;

          &.card-notification {
            top: 20%;
            right: -10%;
            animation-delay: 0s;
          }

          &.card-approval {
            bottom: 30%;
            left: -15%;
            animation-delay: 1.5s;
          }

          &.card-analytics {
            top: 60%;
            right: -5%;
            animation-delay: 3s;
          }
        }
      }
    }

    @media (max-width: 1024px) {
      .visual-container .dashboard-main {
        transform: none;

        &:hover {
          transform: scale(1.02);
        }
      }

      .floating-cards {
        display: none;
      }
    }
  }
}

// Animations modernes
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(2deg);
  }
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-25px) translateX(15px) scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes progressFill {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes dashboardFloat {
  0%,
  100% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(0px);
  }
  50% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(-10px);
  }
}

@keyframes cardPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

@keyframes barGrow {
  0% {
    transform: scaleY(0);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes floatCard {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-15px) rotate(1deg);
    opacity: 1;
  }
}
