<!-- HERO SECTION MODERNE - DESIGN PREMIUM -->
<section class="hero-section" #heroSection>
  <!-- Arrière-plan animé avec gradients -->
  <div class="hero-background">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
    <div class="floating-elements">
      <div class="floating-element element-1"></div>
      <div class="floating-element element-2"></div>
      <div class="floating-element element-3"></div>
      <div class="floating-element element-4"></div>
    </div>
  </div>

  <div class="container">
    <div class="hero-grid">
      <!-- Contenu principal -->
      <div class="hero-content">
        <!-- Badge moderne avec animation -->
        <div class="hero-badge" data-aos="fade-up">
          <div class="badge-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <span>Plateforme RH #1 en France</span>
          <div class="badge-glow"></div>
        </div>

        <!-- Titre principal avec effet de typing -->
        <h1 class="hero-title" data-aos="fade-up" data-aos-delay="100">
          <span class="title-line">Révolutionnez vos</span>
          <span class="title-highlight">Ressources Humaines</span>
          <span class="title-line">avec CompulseHR</span>
        </h1>

        <!-- Sous-titre moderne -->
        <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="200">
          La plateforme RH tout-en-un qui transforme votre gestion des talents.
          SIRH intelligent, paie automatisée, recrutement optimisé et analytics
          avancés dans une interface moderne et intuitive.
        </p>

        <!-- Actions avec micro-interactions -->
        <div class="hero-actions" data-aos="fade-up" data-aos-delay="300">
          <button
            (click)="onDemoClick()"
            class="btn-primary"
            (mouseenter)="onCardHover($event)"
          >
            <span class="btn-text">Demander une démo</span>
            <div class="btn-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path
                  d="M5 12h14M12 5l7 7-7 7"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div class="btn-ripple"></div>
          </button>
          <button
            (click)="onTrialClick()"
            class="btn-secondary"
            (mouseenter)="onCardHover($event)"
          >
            <span class="btn-text">Essai gratuit 14 jours</span>
            <div class="btn-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path
                  d="M8 6l4-4 4 4M12 2v10.3a4 4 0 0 1-1.172 2.828L4 22"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </button>
        </div>

        <!-- Statistiques animées -->
        <div class="hero-stats" data-aos="fade-up" data-aos-delay="400">
          <div class="stats-grid">
            <div
              class="stat-item"
              *ngFor="let stat of stats; let i = index"
              [attr.data-index]="i"
            >
              <div class="stat-number">{{ stat.current }}{{ stat.suffix }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-progress">
                <div class="progress-bar"></div>
              </div>
            </div>
          </div>
          <div class="trust-badges">
            <div class="trust-text">Certifié par</div>
            <div class="badges">
              <div class="badge-item">ISO 27001</div>
              <div class="badge-item">RGPD</div>
              <div class="badge-item">SOC 2</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Visuel moderne avec animations 3D -->
      <div class="hero-visual" data-aos="fade-up" data-aos-delay="500">
        <div class="visual-container">
          <!-- Dashboard principal -->
          <div class="dashboard-main">
            <div class="dashboard-header">
              <div class="header-dots">
                <span class="dot red"></span>
                <span class="dot yellow"></span>
                <span class="dot green"></span>
              </div>
              <div class="header-title">CompulseHR Dashboard</div>
            </div>
            <div class="dashboard-content">
              <div class="dashboard-cards">
                <div class="card card-1">
                  <div class="card-icon">👥</div>
                  <div class="card-value">1,247</div>
                  <div class="card-label">Employés</div>
                </div>
                <div class="card card-2">
                  <div class="card-icon">📊</div>
                  <div class="card-value">94%</div>
                  <div class="card-label">Satisfaction</div>
                </div>
                <div class="card card-3">
                  <div class="card-icon">⚡</div>
                  <div class="card-value">40%</div>
                  <div class="card-label">Gain temps</div>
                </div>
              </div>
              <div class="dashboard-chart">
                <div class="chart-bars">
                  <div class="bar" style="height: 60%"></div>
                  <div class="bar" style="height: 80%"></div>
                  <div class="bar" style="height: 45%"></div>
                  <div class="bar" style="height: 90%"></div>
                  <div class="bar" style="height: 70%"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Cartes flottantes -->
          <div class="floating-cards">
            <div class="floating-card card-notification">
              <div class="notification-icon">🔔</div>
              <div class="notification-text">Nouveau candidat</div>
            </div>
            <div class="floating-card card-approval">
              <div class="approval-icon">✅</div>
              <div class="approval-text">Congé approuvé</div>
            </div>
            <div class="floating-card card-analytics">
              <div class="analytics-icon">📈</div>
              <div class="analytics-text">Performance +15%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
