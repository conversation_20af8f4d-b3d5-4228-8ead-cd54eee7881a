/* === NAVBAR MODERNE ÉPURÉE === */
.modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;

  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    .brand-logo {
      filter: none;
    }

    .nav-link {
      color: #374151;
    }

    .mobile-menu-toggle span {
      background: #374151;
    }
  }
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 80px;

  @media (max-width: 768px) {
    padding: 16px 24px;
    min-height: 70px;
  }
}

.navbar-brand {
  display: flex;
  align-items: center;
  z-index: 1001;

  .brand-logo {
    height: 40px;
    width: auto;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1);

    &:hover {
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      height: 36px;
    }
  }
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 48px;

  @media (max-width: 1024px) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: center;
    gap: 32px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    padding: 32px;

    &.active {
      transform: translateX(0);
    }
  }

  .nav-group {
    display: flex;
    align-items: center;
    gap: 32px;

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 24px;
    }
  }

  .nav-link {
    font-family: "Inter", sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 8px 0;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;

    &:hover {
      color: #3b82f6;
    }

    .dropdown-icon {
      transition: transform 0.3s ease;
    }
  }

  .nav-item {
    position: relative;

    &.has-dropdown:hover .dropdown-icon {
      transform: rotate(180deg);
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 16px;
    min-width: 320px;
    z-index: 1000;
    border: 1px solid rgba(0, 0, 0, 0.05);

    .dropdown-title {
      font-size: 14px;
      font-weight: 600;
      color: #6b7280;
      margin-bottom: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: #f9fafb;
      }

      .item-icon {
        font-size: 20px;
      }

      .item-name {
        font-weight: 600;
        color: #111827;
        display: block;
      }

      .item-desc {
        font-size: 14px;
        color: #6b7280;
        display: block;
      }
    }
  }

  .navbar-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .btn-ghost {
      font-family: "Inter", sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }
    }

    .btn-primary {
      font-family: "Inter", sans-serif;
      font-size: 16px;
      font-weight: 600;
      color: white;
      background: #3b82f6;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }
  }
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  z-index: 1001;

  @media (max-width: 1024px) {
    display: flex;
  }

  span {
    width: 24px;
    height: 2px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    transition: all 0.3s ease;
  }

  &.active {
    span:nth-child(1) {
      transform: rotate(45deg) translate(6px, 6px);
    }

    span:nth-child(2) {
      opacity: 0;
    }

    span:nth-child(3) {
      transform: rotate(-45deg) translate(6px, -6px);
    }
  }
}


