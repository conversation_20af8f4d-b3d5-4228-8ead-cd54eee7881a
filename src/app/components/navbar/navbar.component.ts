import { Component, HostListener, OnInit } from '@angular/core';
import * as AOS from 'aos';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit {
  isMenuOpen = false;
  activeDropdown: string | null = null;
  private dropdownTimeout: any;
  isScrolled = false;

  constructor() {}

  ngOnInit(): void {
    // Initialiser AOS
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100,
    });

    // Détecter le scroll pour changer l'apparence de la navbar
    this.handleScroll();
  }

  toggleMobileMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    // Empêcher le scroll du body quand le menu mobile est ouvert
    if (this.isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  showDropdown(menu: string): void {
    if (this.dropdownTimeout) {
      clearTimeout(this.dropdownTimeout);
    }
    this.activeDropdown = menu;
  }

  hideDropdown(): void {
    this.dropdownTimeout = setTimeout(() => {
      this.activeDropdown = null;
    }, 200); // Délai pour permettre la navigation dans le menu
  }

  private handleScroll(): void {
    window.addEventListener('scroll', () => {
      this.isScrolled = window.scrollY > 50;
    });
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.navbar')) {
      this.isMenuOpen = false;
      this.activeDropdown = null;
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    if (window.innerWidth > 768) {
      this.isMenuOpen = false;
    }
  }
}
