/* === SECTION SÉCURITÉ === */
.seamless-security {
  padding: 120px 0;
  background: white;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 80% 20%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    );
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

/* === EXPANSION GLOBALE === */
.global-expansion {
  margin-bottom: 120px;

  .expansion-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .expansion-text {
    .section-badge {
      display: inline-block;
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 24px;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #0f172a;
      margin-bottom: 24px;
      line-height: 1.2;
      letter-spacing: -0.02em;

      @media (max-width: 1024px) {
        font-size: 2.25rem;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: #64748b;
      max-width: 500px;
      margin-bottom: 32px;

      @media (max-width: 1024px) {
        margin: 0 auto 32px;
      }

      @media (max-width: 768px) {
        font-size: 1rem;
        line-height: 1.5;
      }
    }

    .security-features {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #374151;
        font-weight: 500;

        svg {
          color: #3b82f6;
          flex-shrink: 0;
        }

        span {
          font-size: 16px;
        }
      }

      @media (max-width: 768px) {
        align-items: center;
      }
    }
  }

  .expansion-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .world-map {
      position: relative;
      max-width: 500px;
      width: 100%;

      .map-image {
        width: 100%;
        height: auto;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      }

      .floating-countries {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .country-card {
          position: absolute;
          background: white;
          border-radius: 12px;
          padding: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          border: 1px solid rgba(0, 0, 0, 0.05);
          display: flex;
          align-items: center;
          gap: 12px;
          min-width: 180px;
          pointer-events: auto;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          background: rgba(255, 255, 255, 0.95);

          &:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
          }

          // Positionnement des cartes sur la carte
          &.rdc {
            top: 45%;
            left: 52%;
            animation: float 3s ease-in-out infinite;
          }

          &.morocco {
            top: 25%;
            left: 48%;
            animation: float 3s ease-in-out infinite 0.5s;
          }

          &.senegal {
            top: 35%;
            left: 42%;
            animation: float 3s ease-in-out infinite 1s;
          }

          &.ivory-coast {
            top: 40%;
            left: 45%;
            animation: float 3s ease-in-out infinite 1.5s;
          }

          .country-flag {
            flex-shrink: 0;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            svg {
              display: block;
              border-radius: 4px;
            }
          }

          .country-info {
            flex: 1;

            .country-name {
              font-family: "DM Sans", sans-serif;
              font-size: 14px;
              font-weight: 700;
              color: #0f172a;
              margin: 0 0 4px 0;
            }

            .country-status {
              font-size: 12px;
              color: #3b82f6;
              margin: 0;
              font-weight: 500;
            }
          }

          .security-badge {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6;

            svg {
              width: 16px;
              height: 16px;
            }
          }

          @media (max-width: 768px) {
            min-width: 140px;
            padding: 12px;
            gap: 8px;

            .country-flag svg {
              width: 24px;
              height: 18px;
            }

            .country-info {
              .country-name {
                font-size: 12px;
              }

              .country-status {
                font-size: 10px;
              }
            }

            .security-badge {
              width: 24px;
              height: 24px;

              svg {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }
    }

    .world-map-3d {
      position: relative;
      max-width: 500px;
      width: 100%;

      .map-container {
        position: relative;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(
            circle at 30% 40%,
            rgba(59, 130, 246, 0.05) 0%,
            transparent 50%
          );
          z-index: 1;
        }

        .world-svg {
          width: 100%;
          height: auto;
          position: relative;
          z-index: 2;

          .dot {
            fill: #3b82f6;
            opacity: 0;
            animation: dotAppear 0.5s ease-out forwards;
            filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
          }

          .connection-line {
            stroke: #60a5fa;
            stroke-width: 1;
            opacity: 0;
            stroke-dasharray: 5, 5;
            animation: lineAppear 1s ease-out forwards,
              linePulse 2s ease-in-out infinite;
          }
        }

        .security-indicators {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 3;

          .indicator {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .indicator-pulse {
              width: 12px;
              height: 12px;
              background: #3b82f6;
              border-radius: 50%;
              position: relative;
              animation: indicatorPulse 2s infinite;

              &::before {
                content: "";
                position: absolute;
                top: -4px;
                left: -4px;
                right: -4px;
                bottom: -4px;
                background: rgba(59, 130, 246, 0.3);
                border-radius: 50%;
                animation: ripple 2s infinite;
              }
            }

            .indicator-label {
              font-size: 12px;
              font-weight: 600;
              color: #3b82f6;
              background: white;
              padding: 4px 8px;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              border: 1px solid rgba(59, 130, 246, 0.2);
            }
          }
        }
      }
    }
  }
}

/* === SÉCURITÉ FORTRESS === */
.fortress-security {
  .security-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .security-text {
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 24px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .certifications {
    display: flex;
    justify-content: center;
    gap: 60px;

    @media (max-width: 768px) {
      gap: 40px;
    }

    @media (max-width: 480px) {
      flex-direction: column;
      gap: 30px;
    }

    .cert-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .cert-badge {
        width: 80px;
        height: 80px;
        background: var(--gray-50);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);

        .cert-logo {
          height: 50px;
          width: auto;
        }
      }

      .cert-text {
        text-align: center;

        h3 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 4px;
        }

        p {
          font-size: 1rem;
          color: var(--gray-600);
          margin: 0;
        }
      }
    }
  }
}

/* === ANIMATIONS === */
@keyframes dotAppear {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes lineAppear {
  from {
    opacity: 0;
    stroke-dashoffset: 100;
  }
  to {
    opacity: 0.7;
    stroke-dashoffset: 0;
  }
}

@keyframes linePulse {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes indicatorPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .security-section {
    padding: 60px 0;
  }

  .global-expansion {
    margin-bottom: 80px;
  }

  .fortress-security .security-text {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
