.clients-section {
  max-width: 1200px;
  margin: 3rem auto;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 2.5rem 2rem;
  text-align: center;
}
.section-title {
  color: #1a73e8;
  font-size: 2rem;
  margin-bottom: 2.2rem;
  font-weight: 700;
}
.clients-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2.5rem;
  align-items: center;
}
.client-logo {
  height: 2.5rem;
  width: auto;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
  padding: 0.2rem 0.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
  &:hover {
    transform: scale(1.08);
    box-shadow: 0 4px 16px rgba(26, 115, 232, 0.12);
  }
}
@media (max-width: 700px) {
  .clients-section {
    padding: 1.2rem 0.5rem;
    border-radius: 14px;
  }
  .clients-logos {
    gap: 1.2rem;
  }
  .client-logo {
    height: 1.3rem;
  }
}


